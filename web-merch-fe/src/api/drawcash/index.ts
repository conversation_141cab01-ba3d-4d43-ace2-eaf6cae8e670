import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 商户提现API
 */
export class DrawcashApi {
  /**
   * 通道商户钱包余额列表查询
   */
  static chlMerchQueryWalletAmtList = (data: CommonParams) => post<CommonResult>({ url: '/app/merchant/chlMerchQueryWalletAmtList', data });

  /**
   * 商户提现
   */
  static withdrawByWalletBalance = (data: CommonParams) => post({ url: '/app/merchant/withdrawByWalletBalance', data });

  /**
   * 指定通道商户余额查询
   */
  static queryWalletAmt = (data: CommonParams) => post<CommonResult>({ url: '/app/merchant/queryWalletAmt', data });

  /**
   * 通道商户提现记录
   */
  static queryWithdrawTransList = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantQrcodeTransInfo/queryWithdrawTransList', data });

  /**
   * 通道商户提现结算卡
   */
  static queryChnMerchSettleCardInfo = (data: CommonParams) => post<CommonResult>({ url: '/app/settleBankCard/queryChnMerchSettleCardInfo', data });
}
