<template>
  <view class="h-full overflow-y-scroll bg-white p-30rpx">
    <!-- 导航栏占位 -->
    <wd-navbar custom-class="!bg-transparent" :bordered="false" />

    <!-- logo -->
    <view class="mt-100rpx flex justify-center">
      <wd-img :src="require('@/static/images/logo.jpg')" width="140rpx" height="140rpx" radius="8" />
    </view>

    <view class="mb-30rpx mt-100rpx">
      <wd-segmented
        v-model:value="currentUserType" :options="userTypeOptions" :vibrate-short="true"
        @change="onChangeUserType"
      />
    </view>

    <!-- 登录表单 -->
    <view class="form-wrap">
      <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
        <wd-input
          v-model="form.account"
          prop="account"
          placeholder="请输入账号"
          label-width="35px"
          type="number"
          center use-label-slot use-suffix-slot
        >
          <template #label>
            <wd-icon name="user" size="22px" color="gray" />
          </template>
          <template #suffix>
            <!-- 打开账号列表 -->
            <wd-icon
              v-if="accountStoreList.length"
              name="user-avatar" size="20px" color="gray"
              @click="showSelectAccount = true"
            />
          </template>
        </wd-input>

        <wd-input
          v-model="form.password" prop="password"
          placeholder="请输入密码"
          label-width="35px" center use-label-slot
          show-password
        >
          <template #label>
            <wd-icon name="lock-on" size="22px" color="gray" />
          </template>
        </wd-input>
      </wd-form>

      <view class="mb-90rpx flex justify-between text-24rpx">
        <wd-checkbox v-model="isRemember" custom-label-class="label-class">
          <text>记住密码</text>
        </wd-checkbox>
        <view class="flex items-center text-#4d80f0" @click="toResetPwd">
          忘记密码 <i class="i-mdi-help size-24rpx" />
        </view>
      </view>

      <wd-checkbox v-model="isAgree" custom-label-class="label-class">
        <text>我已阅读</text>
        <text text-#4d80f0 @click.stop="toService">
          《服务协议》
        </text>
        和
        <text text-#4d80f0 @click.stop="toPrivacy">
          《隐私权规则》
        </text>
        <text>并同意服务条款</text>
      </wd-checkbox>

      <view class="mb-30rpx mt-20rpx">
        <wd-button type="primary" size="large" block @click="save">
          登录
        </wd-button>
      </view>

      <!-- <view v-if="currentUserTypeMark === 'merch'" class="flex items-center justify-center text-28rpx text-#333333">
        还没有账户 <i class="i-mdi-help h-12px w-12px" />
        <text ml-10rpx text-#4d80f0 @click="toRegister">
          立即注册>>
        </text>
      </view> -->

      <!-- #ifdef MP-WEIXIN -->
      <view class="mb-20rpx mt-100rpx text-center">
        <text class="text-30rpx" @click="handleNoLogin">
          暂不登录
        </text>
      </view>
      <!-- #endif -->

      <!-- 选择账号登录弹框 -->
      <wd-action-sheet v-model="showSelectAccount" title="选择账号登录">
        <view class="max-h-180px overflow-y-scroll">
          <wd-cell-group border>
            <wd-cell
              v-for="(item, key) in accountStoreList" :key="key"
              :title="item.account"
              center clickable
              @click="changeAccount(item)"
            >
              <wd-icon
                name="delete" size="16px" color="#999"
                @click.stop="deleteAccount(item.account)"
              />
            </wd-cell>
            <wd-cell />
          </wd-cell-group>
        </view>
        <view class="p-20px">
          <wd-button type="info" block size="large" @click="showSelectAccount = false">
            取消
          </wd-button>
        </view>
      </wd-action-sheet>
    </view>
    <wd-toast />

    <!-- 协议弹框 -->
    <wd-message-box selector="wd-agreement-box" custom-class="agreement-message-box">
      <text>请先阅读并同意</text>
      <text text-#4d80f0 @click.stop="toService">
        《服务协议》
      </text>
      和
      <text text-#4d80f0 @click.stop="toPrivacy">
        《隐私权规则》
      </text>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { sm4 } from 'sm-crypto';
import { useMessage, useToast } from 'wot-design-uni';
import type { IRecordItem, LoginForm } from './type';
import { UserApi } from '@/api/user/index';
import { UserApi as UserApiOrg } from '@/api-org/user/index';
import storage from '@/utils/storage';
import { Dialog, buildUrlWithParams, clearToken, setToken } from '@/utils';
import { useUserStore } from '@/store';
import { MerchApi } from '@/api/merch';

const toast = useToast();
const messageAgreement = useMessage('wd-agreement-box');

// 选择登录用户类型
const currentUserType = ref('商户');
const userTypeOptions = [
  {
    mark: 'org',
    value: '机构',
    disabled: false,
  },
  {
    mark: 'merch',
    value: '商户',
    disabled: false,
  },
];
const currentUserTypeMark = computed(() => {
  return userTypeOptions.find(item => item.value === currentUserType.value)?.mark;
});

function onChangeUserType() {
  getLoginAccount();
}

// 表单
const formRef = ref<FormInstance | null>(null);

const form: LoginForm = reactive({
  account: '',
  password: '',
  type: 0,
});
// 规则
const rules: FormRules = {
  account: [{ required: true, message: '请输入账号' }],
  password: [{ required: true, message: '请输入密码' }],
};

// 选择账号登录
const showSelectAccount = ref(false);
const accountStoreList = ref<IRecordItem[]>([]);

// 同意协议
const isAgree = ref(false);
// 记住密码
const isRemember = ref(true);

onLoad(() => {
  clearToken();
});

onShow(() => {
  getUserType();
  getLoginAccount();
});

/**
 * 提交
 */
async function save() {
  // 验证表单
  const { valid } = await formRef.value!.validate();
  if (!valid)
    return;

  if (!isAgree.value) {
    return toast.info('请先阅读并同意服务条款');
  }

  let data;

  switch (currentUserTypeMark.value) {
    case 'org':
      data = await UserApiOrg.login(form);
      break;
    case 'merch':
      data = await UserApi.login(form);
      break;
  }

  // 存储token
  setToken(data?.token);

  // 存储登录用户类型
  useUserStore().setUserType(currentUserTypeMark.value as any);

  // 存储用户信息
  const userInfo = { ...data?.loginUser, ...data?.loginUser?.simpleUserInfo };
  delete (userInfo.simpleUserInfo);
  useUserStore().setInfo(userInfo);

  // 存储登录账号
  if (isRemember.value) {
    saveLoginAccount();
  }

  if (currentUserTypeMark.value === 'merch') {
    // 查询商户状态
    queryMerchStatus();
  }
  else if (currentUserTypeMark.value === 'org') {
    queryOrgUserInfo();
  }
}

async function queryOrgUserInfo() {
  const data = await UserApiOrg.getOrgUserInfo();

  useUserStore().setInfo({
    companyName: data.companyName,
    legalName: data.legalName,
  });

  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}

/**
 * 查询商户状态
 */
async function queryMerchStatus() {
  const data = await MerchApi.queryMerchInfo();
  const { merchant, merchantDetail } = data || {};

  // 存储法人信息
  useUserStore().setInfo({
    legalName: merchantDetail?.legalName || '',
  });

  // 0-初始状态 1-人工审核 2-审核不通过 3-审核通过 4-入网成功
  switch (merchant.authStatus) {
    case 0:
      uni.navigateTo({ url: '/pages/report/merch-auth/auth-micro-merch/index' });
      break;
    case 1:
    case 2:
    case 3:
      uni.navigateTo({ url: '/pages/report/merch-auth/auth-result' });
      break;
    case 4:
      if (merchant.haveElecSignatureStatus) {
        uni.navigateTo({ url: '/pages/report/merch-signature/index' });
      }
      else {
        uni.switchTab({ url: '/pages/tab/home/<USER>' });
      }

      uni.switchTab({ url: '/pages/tab/home/<USER>' });
      break;
    default:
      Dialog('商户状态异常，请联系管理员');
      break;
  }
}

// 持久存储key
const LoginRecordStoreKey = computed(() => {
  if (currentUserTypeMark.value === 'org') {
    return 'loginRecordOrg';
  }
  return 'loginRecord';
});
// 密码处理
const secretPwd = {
  key: '0123456789abcdeffedcba9876543210',
  encrypt: (password: string) => {
    return sm4.encrypt(password, secretPwd.key);
  },
  decrypt: (password: string) => {
    return sm4.decrypt(password, secretPwd.key);
  },
};

/** 保存登录账号 */
function saveLoginAccount() {
  const recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey.value) || [];
  const { account, password } = form;
  const recordItem: IRecordItem = {
    account,
    password: secretPwd.encrypt(password),
  };

  // 如果存在先删除, 保证记录唯一且最新
  const hadIndex = recordList.findIndex(item => item.account === account);
  if (hadIndex !== -1) {
    recordList.splice(hadIndex, 1);
  }

  // 插入到最前面
  recordList.unshift(recordItem);

  storage.setJSON(LoginRecordStoreKey.value, recordList);
}

/** 获取登录账号列表 */
function getLoginAccount() {
  const recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey.value) || [];

  recordList.forEach((item) => {
    item.password = secretPwd.decrypt(item.password);
  });

  accountStoreList.value = recordList;

  if (recordList[0]) {
    const { account, password } = recordList[0];
    form.account = account;
    form.password = password;
    isAgree.value = true;
  }
  else {
    form.account = '';
    form.password = '';
  }
}

/** 切换账号 */
function changeAccount(item: IRecordItem) {
  const { account, password } = item;
  form.account = account;
  form.password = password;
  showSelectAccount.value = false;
}

/** 删除账号 */
function deleteAccount(account: string) {
  let recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey.value) || [];
  recordList = recordList.filter(item => item.account !== account);
  storage.setJSON(LoginRecordStoreKey.value, recordList);
  getLoginAccount();
}

function getUserType() {
  const userType = useUserStore().userType;
  const item = userTypeOptions.find(item => item.mark === userType);
  currentUserType.value = item?.value as string;
}

function toResetPwd() {
  if (currentUserTypeMark.value === 'org') {
    uni.navigateTo({ url: '/pages-org/settings/reset-login-pwd' });
  }
  else {
    uni.navigateTo({ url: '/pages/settings/reset-login-pwd' });
  }
}

function toRegister() {
  if (!isAgree.value) {
    messageAgreement
      .confirm({
        title: '温馨提示',
        confirmButtonText: '同意',
        cancelButtonText: '不同意',
      })
      .then(() => {
        isAgree.value = true;
        toRegister();
      })
      .catch((error) => {
        console.log(error);
      });
  }
  else {
    uni.navigateTo({ url: '/pages/common/register/index' });
  }
}

function handleNoLogin() {
  uni.redirectTo({ url: '/pages/common/tourist/index' });
}

function toPrivacy() {
  const url = buildUrlWithParams('/pages/common/webview/index', {
    url: 'https://wsapp.xnwcloud.com/api/app/privacy',
    title: '隐私权规则',
  });
  uni.navigateTo({ url });
}

function toService() {
  const url = buildUrlWithParams('/pages/common/webview/index', {
    url: 'https://wsapp.xnwcloud.com/api/app/userService',
    title: '服务协议',
  });
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
.form-wrap {
  :deep(.wd-input) {
    &.is-cell{
      @apply bg-primary  mb-30rpx rounded;
    }

    .wd-input__label {
      &.is-required {
        padding-left: 0;
      }

      &::after {
        position: absolute;
        top: 10%;
        right: 0;
        left: auto;
        width: 1px;
        height: 80%;
        background-color: gray;
        content: "";
        transform: scale(0.5, 1);
      }
    }

     .wd-input__icon{
      background-color: transparent;
    }
  }
}

:deep(.label-class) {
  @apply !text-24rpx !text-#999 !break-all;
}

:deep(.agreement-message-box) {
  .wd-message-box__content{
    text-align: left;
  }
}
</style>
