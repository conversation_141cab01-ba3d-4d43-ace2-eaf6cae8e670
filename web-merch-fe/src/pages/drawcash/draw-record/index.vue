<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <template #top>
        <view>
          <wd-drop-menu>
            <wd-drop-menu-item
              v-model="where.receivedStatus" :options="receivedStatusOptions"
              :[receivedStatusTitleProp]="'提现状态'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.settlePeriod" :options="settlePeriodOptions"
              :[settlePeriodTitleProp]="'结算周期'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.handleMode" :options="handleModeOptions"
              :[handleModeTitleProp]="'处理方式'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.handleStatus" :options="handleStatusOptions"
              :[handleStatusTitleProp]="'处理状态'"
              @change="reload"
            />
          </wd-drop-menu>
        </view>

        <view class="bg-primary p-20rpx">
          <view class="flex flex-col items-start rounded-md bg-#4D80F0 p-20rpx text-28rpx text-white">
            <custom-datetime-picker
              v-model="whereDate"
              type="date"
              :default-value="whereDate"
              custom-value-class="!text-white"
              @confirm="handleConfirmDate"
            />

            <view class="mt-12px w-full">
              <view class="flex flex-col items-start">
                <text>提现金额(元)</text>
                <view class="mt-6px">
                  <text class="font-bold">
                    {{ withdrawInfo.totalWithdrawAmt }}
                  </text>
                  <text class="ml-6px">
                    共{{ withdrawInfo.totalTransNum }}笔
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>

      <view class="overflow-hidden px-20rpx pt-20rpx">
        <view v-for="(item, key) in datasource" :key="key" class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white" @click="toDetail(item)">
          <view class="rounded-t-lg bg-#f4f4f4 p-16rpx">
            <text class="text-28rpx font-medium">
              {{ item.chnMerchNo }}({{ item.chnMerchName }})
            </text>
          </view>

          <view class="p-16rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="提现金额(元)" :value="item.withdrawAmount" />
              <!-- <wd-cell title="手续费(元)" :value="item.feeAmount" /> -->
              <wd-cell title="提现状态">
                <text :class="receivedStatusMapColor[item.withdrawStatus]">
                  {{ receivedStatusMap[item.withdrawStatus] || '--' }}
                </text>
              </wd-cell>
              <wd-cell v-if="item.withdrawStatus === 3 && item.resDesc" title="失败描述" :value="item.resDesc" />
              <wd-cell title="提现时间" :value="item.createTime" />
            </wd-cell-group>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { NavigationHelper, buildUrlWithParams, deepClone } from '@/utils';
import { DrawcashApi } from '@/api/drawcash';

const pagingRef = ref();

const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive<any>({
  channelCode: '1007',
  settlePeriod: -1, // 结算周期 1-D0提现 2-T1出款
  receivedStatus: -1, // 提现状态 1-出款中 2-已到账 3-出款失败 4-处理中 5-人工审核
  handleMode: -1, // 处理方式 1-重新付款 2-退回钱包
  handleStatus: -1, // 处理状态 0-无需处理 1-等待处理 2-处理完成
  searchBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  searchEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});

const receivedStatusTitleProp = computed(() => {
  const prop = where.receivedStatus === -1 ? 'title' : '';
  return prop;
});
const settlePeriodTitleProp = computed(() => {
  const prop = where.settlePeriod === -1 ? 'title' : '';
  return prop;
});
const handleModeTitleProp = computed(() => {
  const prop = where.handleMode === -1 ? 'title' : '';
  return prop;
});
const handleStatusTitleProp = computed(() => {
  const prop = where.handleStatus === -1 ? 'title' : '';
  return prop;
});

const receivedStatusMap: EnumMap = {
  1: '出款中',
  2: '已到账',
  3: '出款失败',
  4: '处理中',
  5: '人工审核',
};

const receivedStatusOptions = [
  { label: '全部', value: -1 },
  ...Object.entries(receivedStatusMap).map(([key, value]) => ({
    label: value,
    value: Number(key),
  })),
];

const receivedStatusMapColor: EnumMap = {
  1: 'text-blue',
  2: 'text-green',
  3: 'text-red',
  4: 'text-orange',
  5: 'text-yellow',
};

const settlePeriodMap: EnumMap = {
  1: 'D0提现',
  2: 'T1出款',
};
const settlePeriodOptions = [
  { label: '全部', value: -1 },
  ...Object.entries(settlePeriodMap).map(([key, value]) => ({
    label: value,
    value: Number(key),
  })),
];

const handleModeMap: EnumMap = {
  1: '重新付款',
  2: '退回钱包',
};
const handleModeOptions = [
  { label: '全部', value: -1 },
  ...Object.entries(handleModeMap).map(([key, value]) => ({
    label: value,
    value: Number(key),
  })),
];

const handleStatusMap: EnumMap = {
  0: '无需处理',
  1: '等待处理',
  2: '处理完成',
};
const handleStatusOptions = [
  { label: '全部', value: -1 },
  ...Object.entries(handleStatusMap).map(([key, value]) => ({
    label: value,
    value: Number(key),
  })),
];

const datasource = ref<any[]>([]);

const withdrawInfo = ref<any>({
  totalTransNum: 0, // 总笔数
  totalWithdrawAmt: 0, // 提现总金额
});

function toDetail(item: any) {
  NavigationHelper.navigateToWithData('/pages/drawcash/draw-record/draw-record-detail', {
    ...item,
  });
}

function handleConfirmDate({ value }: { value: string[] }) {
  [where.searchBeginTime, where.searchEndTime] = value;
  pagingRef.value.reload();
}

function reload() {
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.receivedStatus = where.receivedStatus === -1 ? null : where.receivedStatus;
  formatWhere.settlePeriod = where.settlePeriod === -1 ? null : where.settlePeriod;
  formatWhere.handleMode = where.handleMode === -1 ? null : where.handleMode;
  formatWhere.handleStatus = where.handleStatus === -1 ? null : where.handleStatus;

  DrawcashApi.queryWithdrawTransList({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      const { totalTransNum, totalWithdrawAmt, pageResult } = res || {};
      withdrawInfo.value.totalTransNum = totalTransNum || 0;
      withdrawInfo.value.totalWithdrawAmt = totalWithdrawAmt || 0;
      pagingRef.value.completeByTotal(pageResult?.rows || [], pageResult?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
