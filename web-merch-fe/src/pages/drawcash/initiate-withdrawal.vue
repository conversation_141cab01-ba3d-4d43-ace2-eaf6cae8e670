<template>
  <view class="h-full bg-primary">
    <view class="p-20rpx">
      <view class="rounded-lg bg-white p-30rpx">
        <view v-if="!isShowSettleCard" class="flex items-center">
          <wd-skeleton :row-col="[{ size: '70rpx', type: 'circle' }]" />
          <wd-skeleton :custom-style="{ width: '100%', marginLeft: '20rpx' }" :row-col="[{ width: '70%' }, { width: '50%' }, { width: '100%' }]" />
        </view>

        <view v-if="isShowSettleCard" class="flex items-center">
          <i class="i-mdi-bank-circle-outline size-70rpx shrink-0 text-red-6" />
          <view class="ml-20rpx flex grow flex-col items-start">
            <text>{{ settleCardInfo.bankName }}</text>
            <text class="my-10rpx">
              {{ settleCardInfo.accountType === 'G' ? '对公' : '对私' }}
            </text>
            <text class="font-semibold">
              {{ settleCardInfo.bankAccountNoMask }}
            </text>
          </view>
        </view>
      </view>

      <view class="mt-30rpx overflow-hidden rounded-lg bg-white px-30rpx">
        <view class="flex border border-b-#edf0f3 border-b-solid py-22rpx">
          <text class="shrink-0 text-#666">
            账户余额
          </text>
          <text class="ml-30rpx grow break-all text-right text-32rpx">
            {{ drawInfo.withdrawalAmt }}
          </text>
        </view>

        <view class="mt-20rpx flex justify-between">
          <text>
            提现金额
          </text>
          <text class="text-#4d80f0" @click="handleAllIn">
            全部提现
          </text>
        </view>

        <view class="my-20rpx flex">
          <text class="mt-20rpx">
            ¥
          </text>
          <view class="ml-10rpx grow">
            <wd-input
              v-model="withdrawAmount"
              custom-class="custom-input-class"
              no-border
              placeholder="0.00"
              type="digit"
            />
          </view>
        </view>
      </view>

      <view class="py-80rpx">
        <wd-button block size="large" :disabled="isBanSave" @click="save">
          提现
        </wd-button>
      </view>
    </view>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni';
import { DrawcashApi } from '@/api/drawcash';
import { Dialog } from '@/utils';

const toast = useToast();

const routeParams = ref<any>({});

const settleCardInfo = ref<any>({});
const isShowSettleCard = computed(() => !!settleCardInfo.value.bankAccountNoMask);

// 提现金额
const withdrawAmount = ref('');

const drawInfo = ref<any>({
  withdrawalAmt: '0.00',
});

const isBanSave = computed(() => {
  return !withdrawAmount.value || !(Number(withdrawAmount.value) > 0);
});

onLoad((query: any) => {
  routeParams.value = Object.assign({}, query);

  querySettleCardInfo();
  queryWalletAmt();
});

async function querySettleCardInfo() {
  const data = await DrawcashApi.queryChnMerchSettleCardInfo({
    chnMerchNo: routeParams.value.chnMerchNo,
    channelCode: routeParams.value.channelCode,
  });

  settleCardInfo.value = Object.assign(settleCardInfo.value, data);
}

/**
 * 查询通道商户余额
 */
async function queryWalletAmt() {
  const data = await DrawcashApi.queryWalletAmt({
    chnMerchNo: routeParams.value.chnMerchNo,
    channelCode: routeParams.value.channelCode,
  });

  drawInfo.value = Object.assign(drawInfo.value, data);
}

async function save() {
  if (Number(withdrawAmount.value) > Number(drawInfo.value.withdrawalAmt)) {
    return Dialog('提现金额不能大于账户余额');
  }

  const params = {
    chnMerchNo: routeParams.value.chnMerchNo,
    channelCode: routeParams.value.channelCode,
    withdrawAmount: withdrawAmount.value,
  };
  DrawcashApi.withdrawByWalletBalance(params).then(() => {
    toast.success({
      msg: '操作成功',
      closed: () => {
        uni.redirectTo({ url: '/pages/drawcash/draw-record/index' });
      },
    });
  });
}

function handleAllIn() {
  withdrawAmount.value = drawInfo.value.withdrawalAmt;
}
</script>

<style lang="scss" scoped>
:deep(.custom-input-class) {
  background-color: transparent !important;

/* stylelint-disable-next-line selector-class-pattern */
.wd-input__inner{
  font-size: 50rpx;
  font-weight: 600;
}
}
</style>
