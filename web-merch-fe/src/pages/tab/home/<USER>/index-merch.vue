<template>
  <view class="h-full">
    <page-paging ref="pagingRef" refresher-only @on-refresh="onRefreshData">
      <view class="banner">
        <view class="banner-bg" />
        <!-- 顶部 -->
        <view class="pos-relative pb-30rpx">
          <!-- 导航栏 -->
          <wd-navbar custom-class="custom-navbar-class" :bordered="false" safe-area-inset-top>
            <!-- 个人信息 -->
            <template #title>
              <view class="px-30rpx text-white font-600" @click="handleToUserCenter">
                <view class="flex shrink-0 basis-1/2 items-center">
                  <i class="i-mdi-account-circle-outline size-60rpx" />
                  <text class="ml-5px text-34rpx">
                    {{ maskPhoneNumber(loginUser.account as string) }}
                  </text>
                </view>
              </view>
            </template>
          </wd-navbar>
        </view>

        <!-- 菜单 -->
        <view class="pos-relative bg-transparent pb-120rpx">
          <wd-grid :column="4" bg-color="transparent" clickable custom-class="custom-grid-class">
            <wd-grid-item v-for="(item, key) in menus" :key="key" use-slot @itemclick="toMenuUrl(item)">
              <view class="flex flex-col items-center text-white">
                <i class="size-74rpx" :class="item.icon_class" />
                <text class="mt-20rpx text-30rpx">
                  {{ item.menu_name }}
                </text>
              </view>
            </wd-grid-item>
          </wd-grid>
        </view>
      </view>

      <!-- 额度使用情况 -->
      <view class="pannel-class">
        <view class="mb-20rpx flex items-center justify-between">
          <text class="font-bold">
            本月额度使用情况
          </text>
          <view class="flex items-center" @click="toTransRule">
            <text class="text-#4d80f0">
              详情
            </text>
            <i class="i-mdi-chevron-right text-50rpx text-#00000040" />
          </view>
        </view>
        <!-- 刷卡交易 -->
        <view>
          <view class="my-10rpx text-30rpx">
            刷卡交易
          </view>
          <view class="trans-progress-class">
            <view class="info-l">
              当日已用:
              <text class="amount">
                ¥:{{ cumulateLimitInfo.posDayCumulate }}
              </text>
            </view>
            <view class="info-r">
              日限额:
              <text class="amount">
                {{ formatAmount(cumulateLimitInfo.posDayPayLimit) }}
              </text>
            </view>
          </view>
          <wd-progress
            :percentage="calculatePercentage(cumulateLimitInfo.posDayCumulate, cumulateLimitInfo.posDayPayLimit)"
            hide-text
          />
          <wd-gap height="8px" />
          <view class="trans-progress-class">
            <view class="info-l">
              当月汇总:
              <text class="amount">
                ¥:{{ cumulateLimitInfo.posMonthCumulate }}
              </text>
            </view>
            <view class="info-r">
              月限额:
              <text class="amount">
                {{ formatAmount(cumulateLimitInfo.posMonthPayLimit) }}
              </text>
            </view>
          </view>
          <wd-progress
            :percentage="calculatePercentage(cumulateLimitInfo.posMonthCumulate, cumulateLimitInfo.posMonthPayLimit)"
            hide-text
          />
        </view>

        <!-- 扫码交易 -->
        <view>
          <view class="my-10rpx text-30rpx">
            扫码交易
          </view>
          <view class="trans-progress-class">
            <view class="info-l">
              当日已用:
              <text class="amount">
                ¥:{{ cumulateLimitInfo.qrcDayCumulate }}
              </text>
            </view>
            <view class="info-r">
              日限额:
              <text class="amount">
                {{ formatAmount(cumulateLimitInfo.qrcDayPayLimit) }}
              </text>
            </view>
          </view>
          <wd-progress
            :percentage="calculatePercentage(cumulateLimitInfo.qrcDayCumulate, cumulateLimitInfo.qrcDayPayLimit)"
            hide-text
          />
          <wd-gap height="8px" />
          <view class="trans-progress-class">
            <view class="info-l">
              当月已用:
              <text class="amount">
                ¥:{{ cumulateLimitInfo.qrcMonthCumulate }}
              </text>
            </view>
            <view class="info-r">
              月限额:
              <text class="amount">
                {{ formatAmount(cumulateLimitInfo.qrcMonthPayLimit) }}
              </text>
            </view>
          </view>
          <wd-progress
            :percentage="calculatePercentage(cumulateLimitInfo.qrcMonthCumulate, cumulateLimitInfo.qrcMonthPayLimit)"
            hide-text
          />
        </view>
      </view>

    <!-- 实时记录 -->
    <!-- <view class="pannel-class">
      <view class="flex items-center justify-between">
        <text class="font-bold">
          实时记录
        </text>
        <view class="flex items-center" @click="toTrans">
          <text class="text-#4d80f0">
            查看更多
          </text>
          <i class="i-mdi-chevron-right text-40rpx text-#00000040" />
        </view>
      </view>

      <view class="text-24rpx">
        <wd-row
          v-for="(item, key) in transList" :key="key" :gutter="5"
          custom-class="flex items-center text-center break-all pt-20rpx"
        >
          <wd-col :span="2">
            <view class="size-40rpx flex items-center justify-center rounded-full bg-#e33c3c p-6rpx">
              <i class="i-mdi-storefront-outline size-full text-white" />
            </view>
          </wd-col>
          <wd-col :span="6">
            <text class="font-bold">
              ¥ {{ item.transAmount }}
            </text>
          </wd-col>
          <wd-col :span="6">
            {{ item.transStatusDesc }}
          </wd-col>
          <wd-col :span="10">
            <text class="text-#666">
              {{ item.transTime }}
            </text>
          </wd-col>
        </wd-row>
      </view>
    </view> -->
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import NP from 'number-precision';
import { useMessage } from 'wot-design-uni';
import { isNumber } from 'wot-design-uni/components/common/util';
import { MerchApi } from '@/api/merch/index';
import { useUserStore } from '@/store';
import { TransApi } from '@/api/trans';
import { CHANNEL_CODE } from '@/config/setting';
import { Dialog } from '@/utils';

defineOptions({
  options: {
    styleIsolation: 'shared', // 启用共享样式
  },
});

const message = useMessage();

// 登录用户信息
const loginUser = computed(() => useUserStore().info);

const pagingRef = ref();

// 实时交易列表 默认查当天最新3条
const transList = ref<any>([]);

// 累计额度信息
const cumulateLimitInfo = ref({
  posDayCumulate: 0,
  posDayPayLimit: 0,
  posMonthCumulate: 0,
  posMonthPayLimit: 0,
  qrcDayCumulate: 0,
  qrcDayPayLimit: 0,
  qrcMonthCumulate: 0,
  qrcMonthPayLimit: 0,
});

// 菜单列表
interface IMenu {
  menu_name: string;
  icon_class: string;
  to: string; // 菜单跳转地址
};
const menus: IMenu[] = [
  {
    menu_name: '商户报备',
    icon_class: 'i-mdi-account-arrow-up-outline',
    to: '/pages/report/merch-report/index',
  },
  {
    menu_name: '交易管理',
    icon_class: 'i-mdi-text-box-outline',
    to: '/pages/trans/index',
  },
  {
    menu_name: '设备管理',
    icon_class: 'i-mdi-calculator',
    to: '/pages/terminal/index',
  },
  {
    menu_name: '结算卡管理',
    icon_class: 'i-mdi-credit-card',
    to: '/pages/settle-info/index',
  },
  // {
  //   menu_name: '合并结算',
  //   icon_class: 'i-mdi-arrow-left-right-bold',
  //   to: '/pages/settle-info/setd0-settle-type',
  // },
  // {
  //   menu_name: '绑卡提额',
  //   icon_class: 'i-mdi-arrow-up-right-bold',
  //   to: '/pages/increase-quota/index',
  // },
];

// 交易状态映射
const transStatusMap: Record<number, string> = {
  1: '已创建',
  2: '交易成功',
  3: '交易失败',
  4: '交易进行中',
  5: '请求已受理',
  6: '支付结果待查',
};

const merchStatus = ref<null | number>(null);

onLoad(() => {
  onRefreshData();
});

function onRefreshData() {
  Promise.allSettled([
    queryMerchStatus(),
    queryCumulateLimitInfo(),
  ]).then(() => {
    pagingRef.value?.complete();
  });
}

/**
 * 查询累计额度信息
 */
async function queryCumulateLimitInfo() {
  const data = await MerchApi.cumulateLimitDetail();
  cumulateLimitInfo.value = Object.assign({}, data, data?.cumulate);
}

/**
 * 查询实时交易列表 默认查当天最新3条
 */
async function queryTransList() {
  const nowDate = dayjs().format('YYYY-MM-DD');
  const data = await TransApi.queryList({
    transChn: CHANNEL_CODE, // 支付机构(传渠道编码）
    searchBeginTime: nowDate, // 查询交易开始时间
    searchEndTime: nowDate, // 查询交易结束时间
    pageNo: 1,
    pageSize: 3,
  });
  // 处理交易状态
  data?.rows.forEach((item: any) => {
    item.transStatusDesc = transStatusMap[item.transStatus];
  });
  transList.value = data?.rows || [];
}

// 手机号脱敏
function maskPhoneNumber(phoneNumber: string): string {
  // 检查手机号是否有效
  if (!/^\d{11}$/.test(phoneNumber)) {
    return phoneNumber;
  }

  // 对手机号进行脱敏处理
  const masked = phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');

  return masked;
}

/**
 * 计算百分比
 * @param value
 * @param totalValue
 */
function calculatePercentage(value: number, totalValue: number) {
  if (!value || !totalValue) {
    return 0;
  }
  // 关闭边界检查
  NP.enableBoundaryChecking(false);
  // 计算百分比
  return NP.round(
    NP.divide(value, totalValue),
    2,
  ) * 100;
}

/**
 * 金额转换
 */
function formatAmount(amount: number) {
  if (!amount)
    return 0;

  amount = Number(amount);
  if (amount < 10000) {
    return amount;
  }
  // 保留两位小数
  else {
    return `${NP.round(NP.divide(amount, 10000), 2)}万`;
  }
}

/**
 * 查询需要意愿核身的订单
 */
async function queryNeedVertOrders() {
  const data = await TransApi.queryNeedInentityVertOrderList();
  const filterData = data?.filter((item: any) => item.isNeedIdentityVert === 1 && item.checkSelfStatus === 0) || [];

  // 存在待支付权益订单
  if (filterData.length)
    showNeedVertOrderPopup();
}

async function queryMerchStatus() {
  const data = await MerchApi.queryStatus();
  merchStatus.value = data?.authStatus;
}

function showNeedVertOrderPopup() {
  message
    .confirm({
      msg: '您存在待支付权益订单, 支付前需要进行意愿确认, 点击确定跳转至意愿确认权益订单列表; 取消后您也可以在“我的-意愿核身订单”查看',
      title: '温馨提示',
    })
    .then(() => {
      uni.navigateTo({ url: '/pages/user/wish-confirm-order' });
    });
}

function toMenuUrl({ to: url }: IMenu) {
  console.log(url);
  if (!isNumber(merchStatus.value)) {
    message.alert({
      msg: '商户状态未知, 请稍后再试',
      title: '提示',
    });
    return;
  }

  switch (merchStatus.value) {
    case 0:
      message.alert({
        msg: '尚未完成实名认证, 点击按钮开始认证',
        title: '温馨提示',
      }).then(() => {
        uni.navigateTo({ url: '/pages/report/merch-auth/auth-micro-merch/index' });
      });
      break;
    case 1:
    case 3:
      message.alert({
        msg: '认证信息审核中, 请耐心等待',
        title: '温馨提示',
      });
      break;
    case 2:
      message.alert({
        msg: '认证信息审核未通过',
        title: '温馨提示',
        confirmButtonText: '查看详情',
      }).then(() => {
        uni.navigateTo({ url: '/pages/report/merch-auth/auth-result' });
      });
      break;
    default:
      uni.navigateTo({ url });
  }
}

function handleToUserCenter() {
  uni.switchTab({ url: '/pages/tab/user/index' });
}

function toTransRule() {
  uni.navigateTo({ url: '/pages/trans/trans-rule' });
}

function toTrans() {
  uni.navigateTo({ url: '/pages/trans/index' });
}
</script>

<style lang="scss" scoped>
.banner {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.banner .banner-bg {
    position: absolute;
    left: -40%;
    z-index: 0;
    width: 180%;
    height: 100%;
    border-bottom-left-radius: 100%;
    border-bottom-right-radius: 100%;
    background-image: linear-gradient(0deg, #517cf0, #254bb2);  /* 渐变 */
    // background-color: #2bb3ed;

}

:deep(.custom-navbar-class){
  background-color: transparent !important;

  .wd-navbar__left{
    padding: 0!important;
  }

  .wd-navbar__title{
  margin:0;
  max-width: 80%;
  color: white !important;
  }
}

:deep(.custom-grid-class){
  .wd-grid-item{
    background-color: transparent !important;
  }

  .wd-grid-item__content{
    padding: 0;
    margin-bottom: 10px;
    background-color: transparent !important;
  }
}

.pannel-class{
  position: relative;
  margin-top: -100rpx;
  background-color: #fff;
  box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;

  @apply  mx-12px rounded-xl  p-14px;

}

.trans-progress-class{
  @apply  flex text-28rpx text-#666 ;

  .info-l,.info-r{
    @apply basis-1/2;
  }

  .info-r{
    @apply text-right;
  }

  .amount{
    @apply text-#333 text-30rpx font-500;
  }
}
</style>
