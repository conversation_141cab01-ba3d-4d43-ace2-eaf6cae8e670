<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <template #top>
        <view class="p-20rpx pb-0">
          <wd-notice-bar text="暂只开放海科通道商户提现" prefix="help-circle" :scrollable="false" />
        </view>
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text>
              {{ item.chnMerchNo }}({{ item.chnMerchShortName }})
            </text>
          </view>

          <view class="py-30rpx">
            <view class="flex flex-col items-center px-30rpx">
              <text>总结算金额</text>
              <text class="mt-10rpx text-32rpx">
                {{ item.totalSettleAmt }}
              </text>
            </view>

            <view class="mt-30rpx w-full flex items-center">
              <view class="w-33.3% flex flex-col items-center">
                <text>可提现金额</text>
                <text class="mt-10rpx text-32rpx">
                  {{ item.withdrawalAmt }}
                </text>
              </view>
              <view class="flex grow flex-col items-center border-x-1px border-#edf0f3 border-x-solid">
                <text>T1账户余额</text>
                <text class="mt-10rpx text-32rpx">
                  {{ item.t1AccAmt }}
                </text>
              </view>
              <view class="w-33.3% flex flex-col items-center">
                <view class="flex items-center">
                  <text>冻结金额</text>
                  <view v-if="item.frozenReason" class="flex items-center justify-center" @click="showPromptInfo(item.frozenReason)">
                    <i class="i-mdi-chat-question-outline ml-6rpx" />
                  </view>
                </view>
                <text class="mt-10rpx text-32rpx">
                  {{ item.frozenAmt }}
                </text>
              </view>
            </view>
          </view>

          <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
            <view class="grow py-20rpx" @click="handleFindRecord(item)">
              提现记录
            </view>
            <view class="w-1px bg-#edf0f3" />
            <view class="grow py-20rpx text-#4d80f0" @click="handleInitiate(item)">
              发起提现
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni';
import { DrawcashApi } from '@/api/drawcash';

const message = useMessage();

const pagingRef = ref();

const datasource = ref<any[]>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function queryDataSource() {
  DrawcashApi.chlMerchQueryWalletAmtList({
    channelCode: '1007',
  })
    .then((data) => {
      pagingRef.value.complete(data || []);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

function handleInitiate(item: any) {
  uni.navigateTo({
    url: `/pages/drawcash/initiate-withdrawal?chnMerchNo=${item.chnMerchNo}&channelCode=1007`,
  });
}

function handleFindRecord(item: any) {
  uni.navigateTo({
    url: `/pages/drawcash/draw-record/index?walletType=${item.walletType}`,
  });
}

function showPromptInfo(msg: string) {
  message.alert({
    msg,
    title: '冻结说明',
  });
}
</script>
