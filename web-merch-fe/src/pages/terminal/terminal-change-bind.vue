<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <wd-radio-group v-model="current" custom-class="custom-radio-group-class" shape="dot">
          <wd-radio v-for="(item, key) in datasource" :key="key" :value="JSON.stringify(item)">
            <view class="flex flex-col text-left">
              <view class="cell">
                <text class="cell_label">
                  商户简称:
                </text>
                <text class="cell_value">
                  {{ item.chnMerchName }}
                </text>
              </view>
              <view class="cell">
                <text class="cell_label">
                  商户编号:
                </text>
                <text class="cell_value">
                  {{ item.chnMerchNo }}
                </text>
              </view>
              <view class="cell">
                <text class="cell_label">
                  机构名称:
                </text>
                <text class="cell_value">
                  {{ formatChannelName(item.channelCode) }}
                </text>
              </view>
            </view>
          </wd-radio>
        </wd-radio-group>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="p-40rpx">
          <wd-button block size="large" type="primary" :disabled="!current" @click="save">
            确定换绑
          </wd-button>
        </view>
      </template>
    </page-paging>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni';
import { MerchReportApi } from '@/api/report';
import { CommonApi } from '@/api/common';

const toast = useToast();
const message = useMessage();

const pagingRef = ref();

const datasource = ref<any[]>([]);

const where = reactive({
  unionpayOpenStatus: 1,
});

const current = ref<any>(null);

const channelList = ref<any>([]);

onLoad((query: any) => {
  getChannelList();
});

function queryDataSource() {
  MerchReportApi.findChnMerchList({ ...where })
    .then((data) => {
      // pagingRef.value.complete(data?.chnMerchList || []);
      pagingRef.value.complete([{
        bankAccountName: '123',
        bankName: '123',
        bankAccount: '123',
        mchntCnAbbr: '123',
      }]);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

async function save() {
  const item = JSON.parse(current.value);

  const res = await MerchReportApi.hkpayChlSettleCardsCompare({ chnMerchNo: where.chnMerchNo, channelCode: where.channelCode, bankAccountNo: item.bankAccount });
  // 是否需要补充 0-不需要 1-需要
  const { needSave, resMsg } = res;

  if (![0, 1].includes(needSave)) {
    message.alert({
      msg: resMsg || '业务异常',
      title: '温馨提示',
    });
    return;
  }

  if (needSave) {
    uni.navigateTo({
      url: `/pages/hk-settle-info/replenish-settle-info?chnMerchNo=${where.chnMerchNo}&channelCode=${where.channelCode}&bankAccountNo=${item.bankAccount}`,
    });
  }
  else {
    const params = {
      chnMerchNo: where.chnMerchNo,
      channelCode: where.channelCode,
      accNo: item.bankAccount,
      needSave,
    };
    await MerchReportApi.hkpayCommitChlSettleCard(params);
    toast.success({
      msg: '操作成功',
      closed: () => {
        uni.navigateBack();
      },
    });
  }
}

async function getChannelList() {
  const data = await CommonApi.getChannelList();
  channelList.value = data || [];
}

/** 格式化通道名称 */
function formatChannelName(channelCode: string) {
  const channel = channelList.value.find((item: any) => item.channelCode === channelCode);
  return channel?.channelName || '--';
}
</script>

<style lang="scss" scoped>
:deep(.custom-radio-group-class) {
  .wd-radio{
    padding: 22px;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .is-checked{
    background-color: #f8f9fa;
    border-color:transparent
  }

  .wd-radio__label{
    width: 100%;
  }

  .wd-radio__shape{
    background-color: transparent;
  }
}

.cell {
  display: flex;
  align-items: center;
  margin-bottom: 4px;

  &_label {
    color: #444;
  }

  &_value {
    margin-left: 10rpx;
  }
}
</style>
