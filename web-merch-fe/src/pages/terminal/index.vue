<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDatasource">
      <!-- 分割块 -->
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx border border-#edf0f3 rounded-lg border-solid bg-white"
        >
          <!-- SN -->
          <view class="flex items-center rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx font-bold">
            <text class="text-15px">
              SN号: {{ item.termSn }}
            </text>
            <i v-if="item.termSn" class="copy-icon ml-2px" @click="copyContent(item.termSn)" />
          </view>

          <view class="p-8px">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="通道名称" :value="item.channelName" />
              <wd-cell title="通道商户编号">
                <view class="flex items-center justify-end">
                  <text>{{ item.chnMerchNo }}</text>
                  <i v-if="item.chnMerchNo" class="copy-icon" @click="copyContent(item.chnMerchNo)" />
                </view>
              </wd-cell>
              <wd-cell title="通道商户名称">
                <view class="flex items-center justify-end">
                  <text>{{ item.chnMerchName }}</text>
                  <i v-if="item.chnMerchName" class="copy-icon" @click="copyContent(item.chnMerchName)" />
                </view>
              </wd-cell>
              <wd-cell title="通道商户绑定时间" :value="item.bindCompleteTime || '--'" />
              <wd-cell title="换绑状态" :value="bindStatusMap[item.bindStatus] || '--'" />
              <wd-cell title="换绑操作时间" :value="item.createTime" />
              <wd-cell v-if="item.bindStatus === 3 && item.bindMessage" title="失败原因" :value="item.bindMessage" />
            </wd-cell-group>
          </view>

          <view class="flex items-center justify-end border border-#f4f4f4 border-t-solid p-20rpx">
            <wd-tag custom-class="space" round @click="toCostDetail(item)">
              费用明细
            </wd-tag>
            <!-- <wd-tag custom-class="space" round @click="toChangeBind(item)">
              换绑
            </wd-tag> -->
          </view>
        </view>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="p-40rpx">
          <wd-button block size="large" type="primary" @click="toBindTerminal">
            终端绑定
          </wd-button>
        </view>
      </template>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { TerminalApi } from '@/api/terminal';
import { useClipboard } from '@/hooks';

const pagingRef = ref();

const datasource = ref<Record<string, any>>([]);

const chnMerchNo = ref('');
const channelCode = ref('');

type StatusMap = Record<number, string>;
// 终端绑定状态
const bindStatusMap: StatusMap = {
  '-1': '已终止',
  '0': '未处理',
  '1': '处理中',
  '2': '处理成功',
  '3': '处理失败',
  '4': '无需处理',
};

onLoad((query) => {
  chnMerchNo.value = query?.chnMerchNo || '';
  channelCode.value = query?.channelCode || '';
});

onShow(() => {
  pagingRef.value?.reload();
});

function queryDatasource() {
  TerminalApi.queryChlMerchTerminalList({ chnMerchNo: chnMerchNo.value, channelCode: channelCode.value })
    .then((data) => {
      pagingRef.value.complete(data);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

function copyContent(data: string) {
  useClipboard().setClipboardData({ data });
}

function toBindTerminal() {
  uni.navigateTo({ url: `/pages/terminal/terminal-bind?chnMerchNo=${chnMerchNo.value}&channelCode=${channelCode.value}` });
}

function toCostDetail(item: any) {
  uni.navigateTo({ url: `/pages/terminal/cost-detail?terminalSn=${item.termSn}` });
}

function toChangeBind(item: any) {
  uni.navigateTo({ url: `/pages/terminal/terminal-change-bind?terminalSn=${item.termSn}&channelCode=${item.channelCode}` });
}
</script>

<style lang="scss" scoped>
:deep(.space) {
  margin-left: 10rpx;

  &:nth-last-child(1) {
    color: #4d80f0 !important;
    border-color: #4d80f0 !important;
  }

}

:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}

.copy-icon{
  @apply i-mdi-content-copy  size-28rpx text-#b51e1e ml-2px shrink-0;
}
</style>
